---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "amsmath"]
icfes:
  competencia: formulacion_ejecucion
  componente: numerico_variacional
  afirmacion: Resuelve problemas que requieren el planteamiento y estructuración de soluciones ante contextos problémicos
  evidencia: Utiliza operaciones básicas para resolver problemas de aplicación comercial
  nivel: 2
  tematica: Operaciones básicas y aplicaciones comerciales
  contexto: laboral
---

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")
options(scipen = 999)
options(digits = 10)

library(exams)
library(knitr)
library(testthat)
library(digest)

typ <- match_exams_device()
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  echo = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")

# Función para formatear números enteros sin notación científica
formatear_entero <- function(numero) {
  formatC(numero, format = "d", big.mark = "")
}

# Función de formato estándar para números (sin separador de miles, punto decimal)
formato_estandar <- function(x, decimales = 0) {
  if (decimales == 0) {
    return(as.character(as.integer(x)))
  } else {
    resultado <- sprintf(paste0("%.", decimales, "f"), x)
    return(resultado)
  }
}

# Función de redondeo matemático correcto (hacia arriba para .5)
redondear_matematico <- function(x, digits = 0) {
  factor <- 10^digits
  return(floor(x * factor + 0.5) / factor)
}

# Establecer semilla aleatoria para diversidad
set.seed(as.numeric(Sys.time()) + sample(1:10000, 1))

# Aleatorización de contexto comercial
contextos_data <- data.frame(
  nombre = c("tienda", "empresa", "negocio"),
  genero = c("f", "f", "m"),
  articulo = c("una", "una", "un"),
  stringsAsFactors = FALSE
)
contexto_seleccionado <- contextos_data[sample(nrow(contextos_data), 1), ]
contexto <- contexto_seleccionado$nombre
articulo_contexto <- contexto_seleccionado$articulo

# Aleatorización del tipo de comerciante
comerciantes_data <- data.frame(
  nombre = c("vendedor", "empresario", "comerciante"),
  genero = c("m", "m", "m"),
  articulo_el = c("el", "el", "el"),
  articulo_un = c("un", "un", "un"),
  stringsAsFactors = FALSE
)
comerciante_seleccionado <- comerciantes_data[sample(nrow(comerciantes_data), 1), ]
comerciante <- comerciante_seleccionado$nombre
articulo_el_comerciante <- comerciante_seleccionado$articulo_el
articulo_un_comerciante <- comerciante_seleccionado$articulo_un

# Aleatorización del producto
productos_data <- data.frame(
  plural = c("pantalones", "camisas", "zapatos"),
  singular = c("pantalón", "camisa", "zapato"),
  genero = c("m", "f", "m"),
  stringsAsFactors = FALSE
)
producto_seleccionado <- productos_data[sample(nrow(productos_data), 1), ]
producto <- producto_seleccionado$plural
producto_singular <- producto_seleccionado$singular
genero_producto <- producto_seleccionado$genero

# Aleatorización de nombres para el contexto
nombres_masculinos <- c("Pedro", "Carlos", "Miguel", "Antonio", "Diego", "Sebastián")
nombres_femeninos <- c("María", "Ana", "Carmen", "Laura", "Sofía", "Valentina")
nombre_comerciante <- sample(c(nombres_masculinos, nombres_femeninos), 1)

# Aleatorización matemáticamente relevante de precios
tipo_ganancia <- sample(c("baja", "media", "alta"), 1)

if (tipo_ganancia == "baja") {
  precio_compra <- sample(c(80, 90, 100, 120), 1) * 1000
  margen_ganancia <- sample(c(0.10, 0.15, 0.20), 1)  # 10-20% ganancia
} else if (tipo_ganancia == "media") {
  precio_compra <- sample(c(60, 70, 80, 100), 1) * 1000
  margen_ganancia <- sample(c(0.25, 0.30, 0.35), 1)  # 25-35% ganancia
} else {
  precio_compra <- sample(c(50, 60, 70, 80), 1) * 1000
  margen_ganancia <- sample(c(0.40, 0.50, 0.60), 1)  # 40-60% ganancia
}

# Calcular precio de venta basado en el margen
precio_venta <- precio_compra * (1 + margen_ganancia)
ganancia_unitaria <- precio_venta - precio_compra

# Número de unidades vendidas (matemáticamente relevante)
unidades_vendidas <- sample(c(5, 8, 10, 12, 15, 20, 25), 1)
ganancia_total <- ganancia_unitaria * unidades_vendidas

# Información adicional para crear complejidad cognitiva
informacion_irrelevante <- c(
  paste0("La ", contexto, " está ubicada en el centro comercial desde hace ", sample(2:8, 1), " años."),
  paste0("El horario de atención es de ", sample(8:10, 1), ":00 AM a ", sample(6:8, 1), ":00 PM."),
  paste0("Además de ", producto, ", también vende ", sample(c("accesorios", "perfumes", "bolsos"), 1), ".")
)
info_irrelevante_seleccionada <- sample(informacion_irrelevante, 1)

# Datos presentados en orden no secuencial para desarrollar habilidades de identificación
datos_desordenados <- list(
  ganancia_info = paste0("obteniendo una ganancia de $", formatear_entero(ganancia_unitaria), " por cada unidad"),
  precio_venta_info = paste0("los vende a $", formatear_entero(precio_venta), " cada uno"),
  cantidad_info = paste0("vendió ", unidades_vendidas, " ", producto),
  contexto_info = paste0("trabaja en ", articulo_contexto, " ", contexto, " de ", producto)
)

# Respuestas para el formato cloze MEJORADO
respuesta_0 <- "planificacion"    # Paso 0: Planificación estratégica (schoice)
respuesta_1 <- "identificacion"   # Paso 1: Identificación de datos necesarios (schoice)
respuesta_2 <- precio_compra      # Paso 2: Cálculo de precio de compra
respuesta_3 <- ganancia_unitaria  # Paso 3: Verificación de ganancia unitaria
respuesta_4 <- "operacion"        # Paso 4: Identificación de operación (schoice)
respuesta_5 <- ganancia_total     # Paso 5: Cálculo de ganancia total
respuesta_6 <- "verificacion"     # Paso 6: Verificación conceptual (schoice)

# PASO 0: Opciones de planificación estratégica
opciones_planificacion <- c(
  "Identificar datos relevantes → Seleccionar fórmula → Ejecutar cálculos → Verificar resultado",
  "Calcular directamente sin planificar → Verificar al final",
  "Leer todo el problema → Aplicar la primera fórmula que recuerde",
  "Buscar números en el enunciado → Realizar operaciones al azar"
)

# PASO 1: Opciones de identificación de datos
opciones_identificacion <- c(
  "Precio de venta, ganancia unitaria y cantidad vendida",
  "Precio de venta, precio de compra y ubicación de la tienda",
  "Ganancia unitaria, horario de atención y años de funcionamiento",
  "Cantidad vendida, tipo de productos adicionales y ganancia total"
)

# PASO 4: Opciones de operación matemática
opciones_operacion <- c(
  "Multiplicar la ganancia unitaria por la cantidad de unidades vendidas",
  "Sumar la ganancia unitaria más la cantidad de unidades vendidas",
  "Dividir la ganancia unitaria entre la cantidad de unidades vendidas",
  "Restar la cantidad de unidades vendidas de la ganancia unitaria"
)

# PASO 6: Opciones de verificación conceptual (basadas en el ejercicio original)
respuestas_verificacion <- c(
  paste0("Restar $", formatear_entero(precio_compra),
         " al precio de venta de cada ", producto_singular,
         " y multiplicar dicho valor por el número de unidades vendidas."),

  paste0("Calcular la diferencia entre el precio de venta y $", formatear_entero(precio_compra),
         " por cada ", producto_singular,
         ", y multiplicar este resultado por la cantidad de unidades vendidas."),

  paste0("Al precio de venta unitario de cada ", producto_singular,
         " restarle $", formatear_entero(precio_compra),
         " y multiplicar la diferencia obtenida por el total de unidades vendidas.")
)

afirmacion_correcta_verificacion <- sample(respuestas_verificacion, 1)

# Distractores para verificación con errores conceptuales comunes
distractores_verificacion <- c(
  paste0("A $", formatear_entero(precio_compra),
         " restarle el precio de venta de cada ", producto_singular,
         " y multiplicar dicho valor por el número de unidades vendidas."),

  paste0("Multiplicar el precio de venta de cada ", producto_singular,
         " por el número de unidades vendidas y restar $", formatear_entero(precio_compra), "."),

  paste0("Multiplicar $", formatear_entero(precio_compra),
         " por el número de unidades vendidas para obtener la ganancia total.")
)

# Crear opciones finales para cada schoice
opciones_schoice_verificacion <- c(afirmacion_correcta_verificacion, distractores_verificacion)

# Mezclar opciones aleatoriamente para cada schoice
# Paso 0: Planificación
orden_aleatorio_0 <- sample(1:4)
opciones_mezcladas_0 <- opciones_planificacion[orden_aleatorio_0]
indice_respuesta_correcta_0 <- which(orden_aleatorio_0 == 1)
solucion_schoice_0 <- rep(FALSE, 4)
solucion_schoice_0[indice_respuesta_correcta_0] <- TRUE

# Paso 1: Identificación
orden_aleatorio_1 <- sample(1:4)
opciones_mezcladas_1 <- opciones_identificacion[orden_aleatorio_1]
indice_respuesta_correcta_1 <- which(orden_aleatorio_1 == 1)
solucion_schoice_1 <- rep(FALSE, 4)
solucion_schoice_1[indice_respuesta_correcta_1] <- TRUE

# Paso 4: Operación
orden_aleatorio_4 <- sample(1:4)
opciones_mezcladas_4 <- opciones_operacion[orden_aleatorio_4]
indice_respuesta_correcta_4 <- which(orden_aleatorio_4 == 1)
solucion_schoice_4 <- rep(FALSE, 4)
solucion_schoice_4[indice_respuesta_correcta_4] <- TRUE

# Paso 6: Verificación
orden_aleatorio_6 <- sample(1:4)
opciones_mezcladas_6 <- opciones_schoice_verificacion[orden_aleatorio_6]
indice_respuesta_correcta_6 <- which(orden_aleatorio_6 == 1)
solucion_schoice_6 <- rep(FALSE, 4)
solucion_schoice_6[indice_respuesta_correcta_6] <- TRUE

# Vector de soluciones para formato cloze MEJORADO (4 schoice + 3 numéricas)
solucion_cloze <- list(
  solucion_schoice_0,   # Paso 0: Planificación estratégica (schoice)
  solucion_schoice_1,   # Paso 1: Identificación de datos (schoice)
  respuesta_2,          # Paso 2: Precio de compra (numérica)
  respuesta_3,          # Paso 3: Ganancia unitaria (numérica)
  solucion_schoice_4,   # Paso 4: Operación matemática (schoice)
  respuesta_5,          # Paso 5: Ganancia total (numérica)
  solucion_schoice_6    # Paso 6: Verificación conceptual (schoice)
)

# Tipos de respuesta: 4 schoice + 3 numéricas
tipos_respuesta <- c("schoice", "schoice", "num", "num", "schoice", "num", "schoice")

# Tolerancias para respuestas numéricas (solo aplica a las numéricas)
tolerancias <- c(0, 0, 0, 0, 0, 0, 0)

# Detectar formato de salida para ajustes posteriores
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

# PRUEBAS DE VALIDACIÓN MATEMÁTICA MEJORADAS
test_that("Validación de datos generados", {
  expect_true(precio_compra > 0 && precio_venta > 0)
  expect_true(precio_venta > precio_compra)
  expect_equal(ganancia_unitaria, precio_venta - precio_compra)
  expect_equal(ganancia_total, ganancia_unitaria * unidades_vendidas)
  expect_equal(length(solucion_cloze), 7)  # Ahora son 7 respuestas
  expect_equal(length(tipos_respuesta), 7)
  expect_equal(length(tolerancias), 7)
  expect_equal(length(opciones_schoice_verificacion), 4)
  expect_equal(length(unique(opciones_schoice_verificacion)), 4)
})

test_that("Validación de coherencia matemática", {
  expect_equal(respuesta_2, precio_compra)      # Ahora respuesta_2 es precio_compra
  expect_equal(respuesta_3, ganancia_unitaria)  # respuesta_3 es ganancia_unitaria
  expect_equal(respuesta_5, ganancia_total)     # respuesta_5 es ganancia_total
  expect_true(afirmacion_correcta_verificacion %in% opciones_schoice_verificacion)
  expect_true(indice_respuesta_correcta_0 >= 1 && indice_respuesta_correcta_0 <= 4)
  expect_true(indice_respuesta_correcta_1 >= 1 && indice_respuesta_correcta_1 <= 4)
  expect_true(indice_respuesta_correcta_4 >= 1 && indice_respuesta_correcta_4 <= 4)
  expect_true(indice_respuesta_correcta_6 >= 1 && indice_respuesta_correcta_6 <= 4)
})

test_that("Validación de opciones de entrenamiento cognitivo", {
  expect_equal(length(opciones_planificacion), 4)
  expect_equal(length(opciones_identificacion), 4)
  expect_equal(length(opciones_operacion), 4)
  expect_true(nchar(info_irrelevante_seleccionada) > 10)  # Verificar que hay información irrelevante
  expect_equal(length(datos_desordenados), 4)
})
```

Question
========

`r nombre_comerciante` es `r articulo_un_comerciante` `r comerciante` que `r datos_desordenados$contexto_info`. `r info_irrelevante_seleccionada` Para analizar sus ganancias, necesita calcular cuánto dinero obtiene por la venta de estos productos.

En una transacción reciente, `r datos_desordenados$ganancia_info` y `r datos_desordenados$precio_venta_info`. También se sabe que `r datos_desordenados$cantidad_info` en esta operación comercial.

**IMPORTANTE - Formato de números:**

- **Valores monetarios**: Sin separador de miles, use punto para decimales
  - Ejemplo: $16850.32 (no $16.850,32 ni $16850,32)
- **Respuestas numéricas**: Sin separador de miles, use punto para decimales
  - Ejemplo: 1234.5678 (no 1.234,5678 ni 1234,5678)

**INSTRUCCIONES**: Este ejercicio desarrolla habilidades de procesamiento de información y planificación estratégica necesarias para resolver problemas comerciales tipo ICFES. Analice cuidadosamente cada paso:

### Paso 0: Planificación estratégica (DESARROLLO DE AUTONOMÍA COGNITIVA)
Antes de resolver el problema, **seleccione la secuencia de pasos más apropiada** para calcular ganancias comerciales:

##ANSWER1##

### Paso 1: Identificación de datos relevantes (PROCESAMIENTO DE INFORMACIÓN)
Del enunciado anterior, que contiene información variada, **identifique únicamente los datos necesarios** para calcular la ganancia total:

##ANSWER2##

### Paso 2: Cálculo del precio de compra (APLICACIÓN DE FÓRMULAS)
Utilizando la información identificada, calcule el precio de compra por unidad:

Si ganancia unitaria = precio de venta - precio de compra, entonces:
Precio de compra = $##ANSWER3##

### Paso 3: Verificación de la ganancia unitaria (VALIDACIÓN MATEMÁTICA)
Verifique que su cálculo es correcto completando la fórmula:

Ganancia unitaria = Precio de venta - Precio de compra
Ganancia unitaria = $`r formatear_entero(precio_venta)` - $##ANSWER3## = $##ANSWER4##

### Paso 4: Identificación de la operación matemática (RAZONAMIENTO ESTRATÉGICO)
Para calcular la ganancia total, **seleccione la operación matemática correcta**:

##ANSWER5##

### Paso 5: Cálculo de la ganancia total (EJECUCIÓN)
Aplique la operación seleccionada para obtener el resultado final:

Ganancia total = $##ANSWER6##

### Paso 6: Verificación conceptual (CONSOLIDACIÓN DEL APRENDIZAJE)
**Confirme que su proceso de solución es correcto** seleccionando el procedimiento que describe lo que realizó:

##ANSWER7##

**Conclusión:** La ganancia total obtenida por `r nombre_comerciante` en esta transacción es de $##ANSWER6##.

Answerlist
----------
**Paso 0 - Planificación estratégica:**
* `r opciones_mezcladas_0[1]`
* `r opciones_mezcladas_0[2]`
* `r opciones_mezcladas_0[3]`
* `r opciones_mezcladas_0[4]`

**Paso 1 - Identificación de datos:**
* `r opciones_mezcladas_1[1]`
* `r opciones_mezcladas_1[2]`
* `r opciones_mezcladas_1[3]`
* `r opciones_mezcladas_1[4]`

**Paso 4 - Operación matemática:**
* `r opciones_mezcladas_4[1]`
* `r opciones_mezcladas_4[2]`
* `r opciones_mezcladas_4[3]`
* `r opciones_mezcladas_4[4]`

**Paso 6 - Verificación conceptual:**
* `r opciones_mezcladas_6[1]`
* `r opciones_mezcladas_6[2]`
* `r opciones_mezcladas_6[3]`
* `r opciones_mezcladas_6[4]`

Solution
========

### Análisis paso a paso del problema de ganancias comerciales - VERSIÓN MEJORADA PARA ENTRENAMIENTO COGNITIVO

Este problema **desarrolla competencias de procesamiento de información y planificación estratégica** necesarias para resolver exitosamente preguntas tipo ICFES sobre aplicaciones comerciales:

**NOTA IMPORTANTE - Formato de números estandarizado:**

- **Valores monetarios**: Sin separador de miles, use punto para decimales
  - Ejemplo: $16850.32 (no $16.850,32 ni $16850,32)
- **Respuestas numéricas**: Sin separador de miles, punto como separador decimal
  - Ejemplo: 1234.5678 (no 1.234,5678 ni 1234,5678)
- **Consistencia**: Mismo formato en enunciado, opciones y respuestas

### Paso 0: Planificación estratégica ✓ (DESARROLLO DE AUTONOMÍA COGNITIVA)

**Respuesta correcta:** "`r opciones_planificacion[1]`"

**Análisis de opciones:**

```{r mostrar_opciones_planificacion, echo=FALSE, results='asis'}
for(i in 1:4) {
  correcto <- if(i == indice_respuesta_correcta_0) " ← **RESPUESTA CORRECTA**" else ""
  cat(paste0("- **", LETTERS[i], "**: ", opciones_mezcladas_0[i], correcto, "\n"))
}
```

**Justificación:** La planificación estratégica es fundamental para resolver problemas ICFES exitosamente. La secuencia correcta desarrolla pensamiento estructurado.

### Paso 1: Identificación de datos relevantes ✓ (PROCESAMIENTO DE INFORMACIÓN)

**Respuesta correcta:** "`r opciones_identificacion[1]`"

**Análisis de opciones:**

```{r mostrar_opciones_identificacion, echo=FALSE, results='asis'}
for(i in 1:4) {
  correcto <- if(i == indice_respuesta_correcta_1) " ← **RESPUESTA CORRECTA**" else ""
  cat(paste0("- **", LETTERS[i], "**: ", opciones_mezcladas_1[i], correcto, "\n"))
}
```

**Justificación:** Esta habilidad es crucial para ICFES, donde los enunciados contienen información relevante e irrelevante mezclada.

### Paso 2: Cálculo del precio de compra ✓ (APLICACIÓN DE FÓRMULAS)

**Respuesta correcta:** $`r formato_estandar(precio_compra, 0)`

**Razonamiento:**
Si ganancia unitaria = precio de venta - precio de compra, entonces:
$$\text{Precio de compra} = \text{Precio de venta} - \text{Ganancia unitaria}$$
$$\text{Precio de compra} = `r formato_estandar(precio_venta, 0)` - `r formato_estandar(ganancia_unitaria, 0)` = `r formato_estandar(precio_compra, 0)`$$

### Paso 3: Verificación de la ganancia unitaria ✓ (VALIDACIÓN MATEMÁTICA)

**Respuesta correcta:** $`r formato_estandar(ganancia_unitaria, 0)`

**Verificación del cálculo:**
$$\text{Ganancia unitaria} = `r formato_estandar(precio_venta, 0)` - `r formato_estandar(precio_compra, 0)` = `r formato_estandar(ganancia_unitaria, 0)`$$

### Paso 4: Identificación de la operación matemática ✓ (RAZONAMIENTO ESTRATÉGICO)

**Respuesta correcta:** "`r opciones_operacion[1]`"

**Análisis de opciones:**

```{r mostrar_opciones_operacion, echo=FALSE, results='asis'}
for(i in 1:4) {
  correcto <- if(i == indice_respuesta_correcta_4) " ← **RESPUESTA CORRECTA**" else ""
  cat(paste0("- **", LETTERS[i], "**: ", opciones_mezcladas_4[i], correcto, "\n"))
}
```

### Paso 5: Cálculo de la ganancia total ✓ (EJECUCIÓN)

**Respuesta correcta:** $`r formato_estandar(ganancia_total, 0)`

**Cálculo completo:**
$$\text{Ganancia total} = `r formato_estandar(ganancia_unitaria, 0)` \times `r formato_estandar(unidades_vendidas, 0)` = `r formato_estandar(ganancia_total, 0)`$$

### Paso 6: Verificación conceptual ✓ (CONSOLIDACIÓN DEL APRENDIZAJE)

**Respuesta correcta:** "`r afirmacion_correcta_verificacion`"

**Opciones presentadas:**

```{r mostrar_opciones_verificacion, echo=FALSE, results='asis'}
for(i in 1:4) {
  correcto <- if(i == indice_respuesta_correcta_6) " ← **RESPUESTA CORRECTA**" else ""
  cat(paste0("- **", LETTERS[i], "**: ", opciones_mezcladas_6[i], correcto, "\n"))
}
```

**Análisis de la respuesta correcta:**

- Esta opción representa correctamente la fórmula: (Precio de venta - Precio de compra) × Cantidad
- Confirma que el estudiante comprende conceptualmente lo que calculó numéricamente
- Conecta el proceso de cálculo con la comprensión del procedimiento
- Es fundamental para la transferencia hacia preguntas ICFES

**Análisis de distractores:**

- Las demás opciones presentan errores conceptuales comunes:
  - Inversión del orden de la resta (precio de compra - precio de venta)
  - Alteración del orden de las operaciones
  - Confusión entre ganancia total y otros conceptos comerciales

### Verificación del proceso de razonamiento completo

**Datos del problema:**
- Precio de compra: $`r formato_estandar(precio_compra, 0)`
- Precio de venta: $`r formato_estandar(precio_venta, 0)`
- Ganancia unitaria: $`r formato_estandar(ganancia_unitaria, 0)`
- Unidades vendidas: `r formato_estandar(unidades_vendidas, 0)`
- Ganancia total: $`r formato_estandar(ganancia_total, 0)`

**El formato mejorado de entrenamiento cognitivo garantiza que los estudiantes desarrollen:**

**Competencias de Planificación (Paso 0):**
- **Pensamiento estratégico** antes de la ejecución
- **Organización mental** del proceso de solución
- **Autonomía cognitiva** para abordar problemas similares

**Competencias de Procesamiento (Paso 1):**
- **Filtrado de información** relevante vs. irrelevante
- **Identificación de datos** necesarios para la solución
- **Preparación para enunciados ICFES** complejos

**Competencias de Ejecución (Pasos 2-3-5):**
- **Aplicación correcta** de fórmulas matemáticas
- **Cálculos precisos** paso a paso
- **Verificación** de resultados intermedios

**Competencias de Razonamiento (Paso 4):**
- **Selección de operaciones** matemáticas apropiadas
- **Justificación** de estrategias de cálculo
- **Conexión** entre conceptos y procedimientos

**Competencias de Consolidación (Paso 6):**
- **Verificación conceptual** del proceso completo
- **Transferencia** de aprendizaje hacia formato ICFES
- **Coherencia** entre cálculo y comprensión

### Conclusión - Preparación efectiva para ICFES

La ganancia total obtenida por `r nombre_comerciante` en esta transacción es de **$`r formato_estandar(ganancia_total, 0)`**.

**Competencias desarrolladas para enfrentar preguntas ICFES:**

✅ **Procesamiento de información compleja** - El estudiante aprendió a filtrar datos relevantes de enunciados con información mixta

✅ **Planificación estratégica** - Desarrolló la habilidad de estructurar el proceso de solución antes de ejecutar

✅ **Aplicación de fórmulas comerciales** - Domina el cálculo de ganancias en contextos laborales

✅ **Razonamiento matemático** - Puede justificar la selección de operaciones y procedimientos

✅ **Verificación conceptual** - Conecta cálculos numéricos con comprensión del procedimiento

**Transferencia hacia formato ICFES:** Después de este entrenamiento, el estudiante está preparado para resolver preguntas de selección múltiple sobre ganancias comerciales, identificando autónomamente la información relevante y aplicando el procedimiento correcto.

**Verificación adicional**: El margen de ganancia aplicado es del `r formato_estandar(margen_ganancia * 100, 1)`%, lo cual es un margen comercial realista para el tipo de producto (`r producto`).

Meta-information
================
exname: Ganancias Comerciales - Entrenamiento Cognitivo para Preparación ICFES
extype: cloze
exsolution: `r paste(c(mchoice2string(solucion_cloze[[1]]), mchoice2string(solucion_cloze[[2]]), solucion_cloze[[3]], solucion_cloze[[4]], mchoice2string(solucion_cloze[[5]]), solucion_cloze[[6]], mchoice2string(solucion_cloze[[7]])), collapse="|")`
exclozetype: `r paste(tipos_respuesta, collapse="|")`
extol: `r paste(tolerancias, collapse="|")`
exsection: Aritmética|Operaciones básicas|Aplicaciones comerciales|Entrenamiento cognitivo|Preparación ICFES
exextra[Type]: Entrenamiento conceptual
exextra[Program]: R
exextra[Language]: es
exextra[Level]: 3
exextra[Competencia]: Formulación y ejecución
exextra[Componente]: Numérico variacional
exextra[Contexto]: Laboral
exextra[Dificultad]: Media-Alta
exextra[Proposito]: Desarrollo de competencias cognitivas para transferencia a formato ICFES
exextra[Habilidades]: Procesamiento de información|Planificación estratégica|Razonamiento matemático|Verificación conceptual
